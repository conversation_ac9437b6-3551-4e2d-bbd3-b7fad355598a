{"manifest_version": 3, "name": "WhisperLiveKit Tab Capture", "version": "1.0", "description": "Capture and transcribe audio from browser tabs using WhisperLiveKit.", "background": {"service_worker": "background.js"}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "action": {"default_title": "WhisperLiveKit Tab Capture", "default_popup": "popup.html"}, "permissions": ["scripting", "tabCapture", "offscreen", "activeTab", "storage"], "web_accessible_resources": [{"resources": ["requestPermissions.html", "requestPermissions.js"], "matches": ["<all_urls>"]}]}