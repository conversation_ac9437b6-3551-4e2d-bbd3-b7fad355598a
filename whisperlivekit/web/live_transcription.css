:root {
  --bg: #ffffff;
  --text: #111111;
  --muted: #666666;
  --border: #e5e5e5;
  --chip-bg: rgba(0, 0, 0, 0.04);
  --chip-text: #000000;
  --spinner-border: #8d8d8d5c;
  --spinner-top: #b0b0b0;
  --silence-bg: #f3f3f3;
  --loading-bg: rgba(255, 77, 77, 0.06);
  --button-bg: #ffffff;
  --button-border: #e9e9e9;
  --wave-stroke: #000000;
  --label-dia-text: #868686;
  --label-trans-text: #111111;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --bg: #0b0b0b;
    --text: #e6e6e6;
    --muted: #9aa0a6;
    --border: #333333;
    --chip-bg: rgba(255, 255, 255, 0.08);
    --chip-text: #e6e6e6;
    --spinner-border: #555555;
    --spinner-top: #dddddd;
    --silence-bg: #1a1a1a;
    --loading-bg: rgba(255, 77, 77, 0.12);
    --button-bg: #111111;
    --button-border: #333333;
    --wave-stroke: #e6e6e6;
    --label-dia-text: #b3b3b3;
    --label-trans-text: #ffffff;
  }
}

:root[data-theme="dark"] {
  --bg: #0b0b0b;
  --text: #e6e6e6;
  --muted: #9aa0a6;
  --border: #333333;
  --chip-bg: rgba(255, 255, 255, 0.08);
  --chip-text: #e6e6e6;
  --spinner-border: #555555;
  --spinner-top: #dddddd;
  --silence-bg: #1a1a1a;
  --loading-bg: rgba(255, 77, 77, 0.12);
  --button-bg: #111111;
  --button-border: #333333;
  --wave-stroke: #e6e6e6;
  --label-dia-text: #b3b3b3;
  --label-trans-text: #ffffff;
}

:root[data-theme="light"] {
  --bg: #ffffff;
  --text: #111111;
  --muted: #666666;
  --border: #e5e5e5;
  --chip-bg: rgba(0, 0, 0, 0.04);
  --chip-text: #000000;
  --spinner-border: #8d8d8d5c;
  --spinner-top: #b0b0b0;
  --silence-bg: #f3f3f3;
  --loading-bg: rgba(255, 77, 77, 0.06);
  --button-bg: #ffffff;
  --button-border: #e9e9e9;
  --wave-stroke: #000000;
  --label-dia-text: #868686;
  --label-trans-text: #111111;
}

body {
  font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  margin: 0;
  text-align: center;
  background-color: var(--bg);
  color: var(--text);
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Record button */
#recordButton {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background-color: var(--button-bg);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--button-border);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

#recordButton.recording {
  width: 180px;
  border-radius: 40px;
  justify-content: flex-start;
  padding-left: 20px;
}

#recordButton:active {
  transform: scale(0.95);
}

.shape-container {
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.shape {
  width: 25px;
  height: 25px;
  background-color: rgb(209, 61, 53);
  border-radius: 50%;
  transition: all 0.3s ease;
}

#recordButton:disabled .shape {
  background-color: #6e6d6d;
}

#recordButton.recording .shape {
  border-radius: 5px;
  width: 25px;
  height: 25px;
}

/* Recording elements */
.recording-info {
  display: none;
  align-items: center;
  margin-left: 15px;
  flex-grow: 1;
}

#recordButton.recording .recording-info {
  display: flex;
}

.wave-container {
  width: 60px;
  height: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

#waveCanvas {
  width: 100%;
  height: 100%;
}

.timer {
  font-size: 14px;
  font-weight: 500;
  color: var(--text);
  margin-left: 10px;
}

#status {
  margin-top: 15px;
  font-size: 16px;
  color: var(--text);
  margin-bottom: 0;
}

.header-container {
  position: sticky;
  top: 0;
  background-color: var(--bg);
  z-index: 100;
  padding: 20px;
}

/* Settings */
.settings-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.settings {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 12px;
}

.field {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 3px;
}

#chunkSelector,
#websocketInput,
#themeSelector,
#microphoneSelect {
  font-size: 16px;
  padding: 5px 8px;
  border-radius: 8px;
  border: 1px solid var(--border);
  background-color: var(--button-bg);
  color: var(--text);
  max-height: 30px;
}

#microphoneSelect {
  width: 100%;
  max-width: 190px;
  min-width: 120px;
}

#chunkSelector:focus,
#websocketInput:focus,
#themeSelector:focus,
#microphoneSelect:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
}

label {
  font-size: 13px;
  color: var(--muted);
}

.ws-default {
  font-size: 12px;
  color: var(--muted);
}

/* Segmented pill control for Theme */
.segmented {
  display: inline-flex;
  align-items: stretch;
  border: 1px solid var(--button-border);
  background-color: var(--button-bg);
  border-radius: 999px;
  overflow: hidden;
}

.segmented input[type="radio"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.theme-selector-container {
  display: flex;
  align-items: center;
  margin-top: 17px;
}

.segmented label {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  font-size: 14px;
  color: var(--muted);
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.segmented label span {
  display: none;
}

.segmented label:hover span {
  display: inline;
}

.segmented label:hover {
  background-color: var(--chip-bg);
}

.segmented img {
  width: 16px;
  height: 16px;
}

.segmented input[type="radio"]:checked + label {
  background-color: var(--chip-bg);
  color: var(--text);
}

.segmented input[type="radio"]:focus-visible + label,
.segmented input[type="radio"]:focus + label {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-radius: 999px;
}

.transcript-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.transcript-container::-webkit-scrollbar {
  display: none;
}

/* Transcript area */
#linesTranscript {
  margin: 0 auto;
  max-width: 700px;
  text-align: left;
  font-size: 16px;
}

#linesTranscript p {
  margin: 0px 0;
}

#linesTranscript strong {
  color: var(--text);
}

#speaker {
  border: 1px solid var(--border);
  border-radius: 100px;
  padding: 2px 10px;
  font-size: 14px;
  margin-bottom: 0px;
}

.label_diarization {
  background-color: var(--chip-bg);
  border-radius: 8px 8px 8px 8px;
  padding: 2px 10px;
  margin-left: 10px;
  display: inline-block;
  white-space: nowrap;
  font-size: 14px;
  margin-bottom: 0px;
  color: var(--label-dia-text);
}

.label_transcription {
  background-color: var(--chip-bg);
  border-radius: 8px 8px 8px 8px;
  padding: 2px 10px;
  display: inline-block;
  white-space: nowrap;
  margin-left: 10px;
  font-size: 14px;
  margin-bottom: 0px;
  color: var(--label-trans-text);
}

#timeInfo {
  color: var(--muted);
  margin-left: 10px;
}

.textcontent {
  font-size: 16px;
  padding-left: 10px;
  margin-bottom: 10px;
  margin-top: 1px;
  padding-top: 5px;
  border-radius: 0px 0px 0px 10px;
}

.buffer_diarization {
  color: var(--label-dia-text);
  margin-left: 4px;
}

.buffer_transcription {
  color: #7474748c;
  margin-left: 4px;
}

.spinner {
  display: inline-block;
  width: 8px;
  height: 8px;
  border: 2px solid var(--spinner-border);
  border-top: 2px solid var(--spinner-top);
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  vertical-align: middle;
  margin-bottom: 2px;
  margin-right: 5px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.silence {
  color: var(--muted);
  background-color: var(--silence-bg);
  font-size: 13px;
  border-radius: 30px;
  padding: 2px 10px;
}

.loading {
  color: var(--muted);
  background-color: var(--loading-bg);
  border-radius: 8px 8px 8px 0px;
  padding: 2px 10px;
  font-size: 14px;
  margin-bottom: 0px;
}

/* for smaller screens */
@media (max-width: 768px) {
  .header-container {
    padding: 15px;
  }
  
  .settings-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .settings {
    justify-content: center;
    gap: 8px;
  }
  
  .field {
    align-items: center;
  }
  
  #websocketInput,
  #microphoneSelect {
    min-width: 100px;
    max-width: 160px;
  }
  
  .theme-selector-container {
    margin-top: 10px;
  }
  
  .transcript-container {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 10px;
  }
  
  .settings {
    flex-direction: column;
    align-items: center;
    gap: 6px;
  }
  
  #websocketInput,
  #microphoneSelect {
    max-width: 140px;
  }
  
  .segmented label {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .segmented img {
    width: 14px;
    height: 14px;
  }
  
  .transcript-container {
    padding: 10px;
  }
}
